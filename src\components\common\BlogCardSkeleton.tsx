import React from "react";
import { cn } from "@/lib/utils";

type BlogCardSkeletonProps = {
  isLarge?: boolean;
  isMedium?: boolean;
  isSmallMedium?: boolean;
};

const BlogCardSkeleton = ({
  isLarge = false,
  isMedium = false,
  isSmallMedium = false,
}: BlogCardSkeletonProps) => {
  return (
    <div className="h-full animate-pulse">
      <div className="h-full overflow-hidden rounded-[10px] bg-gray-200 shadow-lg">
        <div className={cn(
          "bg-gray-300",
          isLarge || isMedium ? "h-56" : isSmallMedium ? "h-52" : "h-48"
        )}></div>
        <div className={cn(
          "flex flex-col",
          isLarge || isMedium ? "px-6 pb-7 pt-5" : isSmallMedium ? "px-5 pb-6 pt-4" : "p-4"
        )}>
          {/* Tags skeleton - show at top for medium/small-medium, at bottom for large */}
          {(isMedium || isSmallMedium) && (
            <div className="mb-3 flex flex-wrap gap-2">
              <div className={cn(
                "bg-gray-300 rounded-[20px]",
                isSmallMedium ? "h-5 w-14" : "h-6 w-16"
              )}></div>
              <div className={cn(
                "bg-gray-300 rounded-[20px]",
                isSmallMedium ? "h-5 w-18" : "h-6 w-20"
              )}></div>
            </div>
          )}

          {/* Title skeleton */}
          <div className={cn(
            "bg-gray-300 rounded mb-2",
            isLarge || isMedium ? "h-6" : isSmallMedium ? "h-5" : "h-5"
          )}></div>
          <div className={cn(
            "bg-gray-300 rounded w-3/4",
            isLarge || isMedium ? "h-6 mb-1" : isSmallMedium ? "h-5 mb-1" : "h-5 mb-1"
          )}></div>

          {/* Content skeleton */}
          <div className={cn(
            "space-y-2",
            isLarge ? "mb-4" : isSmallMedium ? "" : "mb-4"
          )}>
            <div className={cn(
              "bg-gray-300 rounded",
              isSmallMedium ? "h-3" : "h-4"
            )}></div>
            <div className={cn(
              "bg-gray-300 rounded",
              isSmallMedium ? "h-3" : "h-4"
            )}></div>
            <div className={cn(
              "bg-gray-300 rounded w-5/6",
              isSmallMedium ? "h-3" : "h-4"
            )}></div>
            {(isLarge || isMedium) && (
              <div className="h-4 bg-gray-300 rounded w-2/3"></div>
            )}
          </div>

          {/* Tags skeleton for large cards (at bottom) and normal cards (at top) */}
          {isLarge ? (
            <div className="mt-auto flex flex-wrap gap-2">
              <div className="h-6 w-16 bg-gray-300 rounded-[20px]"></div>
              <div className="h-6 w-20 bg-gray-300 rounded-[20px]"></div>
            </div>
          ) : !isMedium && !isSmallMedium && (
            <div className="mb-3 flex flex-wrap gap-2">
              <div className="h-6 w-16 bg-gray-300 rounded-full"></div>
              <div className="h-6 w-20 bg-gray-300 rounded-full"></div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default BlogCardSkeleton;
