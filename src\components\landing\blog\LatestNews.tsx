"use client";

import React, { useEffect } from "react";
import BlogCard from "./BlogCard";
import { cn } from "@/lib/utils";
import { useBlog } from "@/contexts/BlogContext";
import LoadingSpinner from "@/components/common/LoadingSpinner";

const LatestNews = () => {
  const {
    latestBlogs,
    latestBlogsLoading,
    latestBlogsError,
    fetchLatestBlogs,
  } = useBlog();

  useEffect(() => {
    fetchLatestBlogs();
  }, []);

  if (latestBlogsLoading) {
    return (
      <div className="w-full">
        <h2 className="mb-5 text-3xl font-bold text-accent lg:text-5xl">
          Latest News
        </h2>
        <div className="flex items-center justify-center py-12">
          <LoadingSpinner width={40} height={40} />
        </div>
      </div>
    );
  }

  if (latestBlogsError) {
    return (
      <div className="w-full">
        <h2 className="mb-5 text-3xl font-bold text-accent lg:text-5xl">
          Latest News
        </h2>
        <div className="py-32 text-center">
          <p className="text-red-500">{latestBlogsError}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      <h2 className="mb-5 text-3xl font-bold text-accent lg:text-5xl">
        Latest News
      </h2>
      {latestBlogs.length === 0 ? (
        <div className="py-32 text-center">
          <p className="text-gray-500">No Data</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-8 md:grid-cols-5">
          {latestBlogs.map((post, index) => (
            <div
              key={post._id}
              className={cn(index === 0 ? "md:col-span-3" : "md:col-span-2")}
            >
              <BlogCard
                id={parseInt(post._id.slice(-6), 16) || 0}
                title={post.title}
                excerpt={post.content.substring(0, 150) + "..."}
                image={
                  "https://images.ctfassets.net/hrltx12pl8hq/28ECAQiPJZ78hxatLTa7Ts/2f695d869736ae3b0de3e56ceaca3958/free-nature-images.jpg?fit=fill&w=1200&h=630"
                }
                tags={post.tags || []}
                isLarge={index === 0}
                isMedium={index === 1}
                slug={post.slug || post._id}
              />
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default LatestNews;
