"use client";

import React, { useEffect } from "react";
import BlogCard from "./BlogCard";
import { cn } from "@/lib/utils";
import { useBlog } from "@/contexts/BlogContext";
import BlogCardSkeleton from "@/components/common/BlogCardSkeleton";

const LatestNews = () => {
  const {
    latestBlogs,
    latestBlogsLoading,
    latestBlogsError,
    fetchLatestBlogs,
  } = useBlog();

  useEffect(() => {
    fetchLatestBlogs();
  }, []);

  return (
    <div className="w-full">
      <h2 className="mb-5 text-3xl font-bold text-accent lg:text-5xl">
        Latest News
      </h2>
      {latestBlogsLoading ? (
        <div className="grid grid-cols-1 gap-8 md:grid-cols-5">
          {/* First card - large */}
          <div className="md:col-span-3">
            <BlogCardSkeleton isLarge />
          </div>
          {/* Remaining cards - medium */}
          <div className="md:col-span-2">
            <BlogCardSkeleton isMedium />
          </div>
          <div className="md:col-span-2">
            <BlogCardSkeleton isMedium />
          </div>
          <div className="md:col-span-2">
            <BlogCardSkeleton isMedium />
          </div>
          <div className="md:col-span-2">
            <BlogCardSkeleton isMedium />
          </div>
        </div>
      ) : latestBlogsError ? (
        <div className="py-32 text-center">
          <p className="text-red-500">{latestBlogsError}</p>
        </div>
      ) : latestBlogs.length === 0 ? (
        <div className="py-32 text-center">
          <p className="text-gray-500">No Data</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-8 md:grid-cols-5">
          {latestBlogs.map((post, index) => (
            <div
              key={post._id}
              className={cn(index === 0 ? "md:col-span-3" : "md:col-span-2")}
            >
              <BlogCard
                id={parseInt(post._id.slice(-6), 16) || 0}
                title={post.title}
                excerpt={post.content.substring(0, 150) + "..."}
                image={
                  "https://images.ctfassets.net/hrltx12pl8hq/28ECAQiPJZ78hxatLTa7Ts/2f695d869736ae3b0de3e56ceaca3958/free-nature-images.jpg?fit=fill&w=1200&h=630"
                }
                tags={post.tags || []}
                isLarge={index === 0}
                isMedium={index === 1}
                slug={post.slug || post._id}
              />
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default LatestNews;
